const express = require('express');
const mongoose = require('mongoose');
const admin = require('firebase-admin');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const { User } = require('./schemas');

const app = express();

// Middleware
app.use(express.json());
app.use(cors()); // Enable CORS for frontend-backend communication

// Environment variables (create a .env file for these)
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key_change_this_in_production';
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/plover-app';
const PORT = process.env.PORT || 5000;

// Initialize Firebase Admin SDK
// You need to download your service account key from Firebase Console
// and either set GOOGLE_APPLICATION_CREDENTIALS environment variable
// or provide the path to the service account key file
try {
  if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    // Using environment variable (recommended for production)
    admin.initializeApp({
      credential: admin.credential.applicationDefault(),
      projectId: 'plover-c2b9b'
    });
  } else if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
    // Using service account key from environment variable
    const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: 'plover-c2b9b'
    });
  } else {
    // For development - you need to download service account key
    console.log('⚠️  Firebase Admin not initialized - no credentials provided');
    console.log('Please set GOOGLE_APPLICATION_CREDENTIALS or FIREBASE_SERVICE_ACCOUNT_KEY');
    console.log('Download service account key from Firebase Console > Project Settings > Service Accounts');
  }
} catch (error) {
  console.error('❌ Error initializing Firebase Admin:', error.message);
}

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => console.log('✅ Connected to MongoDB'))
  .catch(err => console.error('❌ MongoDB connection error:', err));

// Middleware to verify Firebase token
const verifyFirebaseToken = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'No token provided' });
  }

  const idToken = authHeader.split('Bearer ')[1];
  
  try {
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    req.user = decodedToken;
    next();
  } catch (error) {
    console.error('Token verification error:', error);
    res.status(401).json({ error: 'Invalid token' });
  }
};

// Middleware to verify JWT token
const verifyJWT = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'No token provided' });
  }

  const token = authHeader.split('Bearer ')[1];
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
};

// ===== AUTHENTICATION ROUTES =====

// Signup API
app.post('/api/signup', async (req, res) => {
  const { email, password, phone, name, idToken } = req.body;
  
  try {
    let firebaseUser;
    
    if (idToken) {
      // User already created in Firebase (frontend), verify the token
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      firebaseUser = await admin.auth().getUser(decodedToken.uid);
    } else {
      // Create user in Firebase (backend)
      firebaseUser = await admin.auth().createUser({ 
        email, 
        password,
        displayName: name 
      });
    }
    
    // Check if user already exists in MongoDB
    const existingUser = await User.findOne({ firebaseUid: firebaseUser.uid });
    if (existingUser) {
      return res.status(400).json({ error: 'User already exists' });
    }
    
    // Create user in MongoDB
    const user = new User({
      firebaseUid: firebaseUser.uid,
      email: firebaseUser.email || email,
      phone,
      password, // Will be hashed by schema pre-save hook
      profile: { 
        name: firebaseUser.displayName || name, 
        personal_details: {}, 
        subscription_history: [{ plan: 'Free', date: new Date() }] 
      },
    });
    
    await user.save();
    
    // Generate JWT
    const token = jwt.sign(
      { uid: firebaseUser.uid, email: user.email }, 
      JWT_SECRET, 
      { expiresIn: '7d' }
    );
    
    res.status(201).json({ 
      token, 
      user: { 
        uid: firebaseUser.uid,
        email: user.email, 
        name: user.profile.name,
        phone: user.phone
      } 
    });
    
  } catch (error) {
    console.error('Signup error:', error);
    res.status(400).json({ error: error.message });
  }
});

// Login API
app.post('/api/login', async (req, res) => {
  const { idToken } = req.body;
  
  try {
    // Verify Firebase token
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    // Find user in MongoDB
    const user = await User.findOne({ firebaseUid: decodedToken.uid });
    if (!user) {
      return res.status(404).json({ error: 'User not found. Please sign up first.' });
    }
    
    // Generate JWT
    const token = jwt.sign(
      { uid: decodedToken.uid, email: user.email }, 
      JWT_SECRET, 
      { expiresIn: '7d' }
    );
    
    // Update last login
    user.updated_at = new Date();
    await user.save();
    
    res.json({ 
      token, 
      user: { 
        uid: user.firebaseUid,
        email: user.email, 
        name: user.profile.name,
        phone: user.phone
      } 
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(401).json({ error: 'Invalid credentials' });
  }
});

// ===== PROTECTED ROUTES =====

// Get user profile
app.get('/api/profile', verifyJWT, async (req, res) => {
  try {
    const user = await User.findOne({ firebaseUid: req.user.uid });
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json({
      uid: user.firebaseUid,
      email: user.email,
      phone: user.phone,
      profile: user.profile,
      friends: user.friends,
      created_at: user.created_at,
      updated_at: user.updated_at
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user profile
app.put('/api/profile', verifyJWT, async (req, res) => {
  try {
    const { name, phone, personal_details } = req.body;
    
    const user = await User.findOne({ firebaseUid: req.user.uid });
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    // Update fields
    if (name) user.profile.name = name;
    if (phone) user.phone = phone;
    if (personal_details) user.profile.personal_details = { ...user.profile.personal_details, ...personal_details };
    
    await user.save();
    
    res.json({ message: 'Profile updated successfully', user: user.profile });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    firebase: admin.apps.length > 0 ? 'Connected' : 'Not Connected',
    mongodb: mongoose.connection.readyState === 1 ? 'Connected' : 'Disconnected'
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`📱 Firebase Project: plover-c2b9b`);
  console.log(`🗄️  MongoDB: ${MONGODB_URI}`);
});

module.exports = app;
