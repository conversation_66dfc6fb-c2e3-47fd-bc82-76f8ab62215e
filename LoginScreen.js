import React, { useState } from 'react';
import { View, Text, TextInput, Button, Alert } from 'react-native';
import auth from '@react-native-firebase/auth';
import axios from 'axios';

const LoginScreen = ({ setUser }) => {
  const [phone, setPhone] = useState('');
  const [confirm, setConfirm] = useState(null);
  const [code, setCode] = useState('');

  const handlePhoneLogin = async () => {
    try {
      const confirmation = await auth().signInWithPhoneNumber(phone);
      setConfirm(confirmation);
    } catch (error) {
      Alert.alert('Error', 'Failed to send OTP: ' + error.message);
    }
  };

  const confirmCode = async () => {
    try {
      const userCredential = await confirm.confirm(code);
      const idToken = await userCredential.user.getIdToken();
      const response = await axios.post('http://localhost:5000/api/login', { idToken });
      setUser(response.data.user);
      // Store token in AsyncStorage (not shown for brevity)
    } catch (error) {
      Alert.alert('Error', 'Invalid OTP: ' + error.message);
    }
  };

  return (
    <View style={{ padding: 20 }}>
      {!confirm ? (
        <>
          <TextInput
            value={phone}
            onChangeText={setPhone}
            placeholder="+919876543210"
            keyboardType="phone-pad"
            style={{ borderWidth: 1, padding: 10, marginBottom: 10 }}
          />
          <Button title="Send OTP" onPress={handlePhoneLogin} />
        </>
      ) : (
        <>
          <TextInput
            value={code}
            onChangeText={setCode}
            placeholder="Enter OTP"
            keyboardType="numeric"
            style={{ borderWidth: 1, padding: 10, marginBottom: 10 }}
          />
          <Button title="Confirm OTP" onPress={confirmCode} />
        </>
      )}
    </View>
  );
};

export default LoginScreen;