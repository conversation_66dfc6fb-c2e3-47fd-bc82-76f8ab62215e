<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Integration Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .error {
            color: #dc3545;
            margin: 10px 0;
        }
        .success {
            color: #28a745;
            margin: 10px 0;
        }
        .user-info {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .hidden {
            display: none;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.online {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.offline {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 Firebase Integration Demo</h1>
        <p>This demo shows the integration between your Firebase frontend and backend server.</p>
        
        <div id="serverStatus" class="status offline">
            <strong>Backend Server:</strong> <span id="statusText">Checking...</span>
        </div>
    </div>

    <!-- Authentication Section -->
    <div class="container" id="authSection">
        <h2>Authentication</h2>
        
        <div id="loginForm">
            <h3>Sign In / Sign Up</h3>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" placeholder="Enter your email" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" placeholder="Enter your password" required>
            </div>
            
            <div class="form-group" id="signupFields" class="hidden">
                <label for="name">Full Name:</label>
                <input type="text" id="name" placeholder="Enter your full name">
                
                <label for="phone" style="margin-top: 10px;">Phone (optional):</label>
                <input type="tel" id="phone" placeholder="Enter your phone number">
            </div>
            
            <div id="errorMessage" class="error"></div>
            <div id="successMessage" class="success"></div>
            
            <button onclick="handleLogin()" id="loginBtn">Sign In</button>
            <button onclick="toggleSignup()" id="toggleBtn">Switch to Sign Up</button>
            <button onclick="testBackend()" style="background-color: #6c757d;">Test Backend</button>
        </div>
    </div>

    <!-- User Dashboard -->
    <div class="container hidden" id="userDashboard">
        <h2>Welcome!</h2>
        <div id="userInfo" class="user-info"></div>
        
        <h3>Update Profile</h3>
        <div class="form-group">
            <label for="updateName">Name:</label>
            <input type="text" id="updateName" placeholder="Your name">
        </div>
        <div class="form-group">
            <label for="updatePhone">Phone:</label>
            <input type="tel" id="updatePhone" placeholder="Your phone">
        </div>
        
        <button onclick="updateProfile()">Update Profile</button>
        <button onclick="upgradeSubscription()" style="background-color: #28a745;">Upgrade to Premium</button>
        <button onclick="addFriend()" style="background-color: #17a2b8;">Add Friend</button>
        <button onclick="logout()" style="background-color: #dc3545;">Logout</button>

        <h3>Add Friend</h3>
        <div class="form-group">
            <label for="friendUid">Friend UID:</label>
            <input type="text" id="friendUid" placeholder="Enter friend's UID">
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        let currentUser = null;
        let authToken = localStorage.getItem('authToken');
        let isSignupMode = false;

        // Check server status on load
        window.onload = function() {
            checkServerStatus();
            if (authToken) {
                getUserProfile();
            }
        };

        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                document.getElementById('statusText').textContent = 'Online ✅';
                document.getElementById('serverStatus').className = 'status online';
                
                console.log('Server status:', data);
            } catch (error) {
                document.getElementById('statusText').textContent = 'Offline ❌ (Make sure backend server is running)';
                document.getElementById('serverStatus').className = 'status offline';
                console.error('Server check failed:', error);
            }
        }

        async function testBackend() {
            try {
                const response = await fetch(`${API_BASE}/test`);
                const data = await response.json();
                showSuccess('Backend test successful: ' + data.message);
            } catch (error) {
                showError('Backend test failed: ' + error.message);
            }
        }

        function toggleSignup() {
            isSignupMode = !isSignupMode;
            const signupFields = document.getElementById('signupFields');
            const loginBtn = document.getElementById('loginBtn');
            const toggleBtn = document.getElementById('toggleBtn');
            
            if (isSignupMode) {
                signupFields.classList.remove('hidden');
                loginBtn.textContent = 'Sign Up';
                toggleBtn.textContent = 'Switch to Sign In';
            } else {
                signupFields.classList.add('hidden');
                loginBtn.textContent = 'Sign In';
                toggleBtn.textContent = 'Switch to Sign Up';
            }
            clearMessages();
        }

        async function handleLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const name = document.getElementById('name').value;
            const phone = document.getElementById('phone').value;

            if (!email || !password) {
                showError('Please fill in email and password');
                return;
            }

            if (isSignupMode && !name) {
                showError('Please fill in your name for signup');
                return;
            }

            try {
                const endpoint = isSignupMode ? '/signup' : '/login';
                const body = isSignupMode 
                    ? { email, password, name, phone }
                    : { email, password };

                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(body)
                });

                const data = await response.json();

                if (response.ok) {
                    authToken = data.token;
                    localStorage.setItem('authToken', authToken);
                    currentUser = data.user;
                    showUserDashboard();
                    showSuccess(isSignupMode ? 'Account created successfully!' : 'Logged in successfully!');
                } else {
                    showError(data.error || 'Authentication failed');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        async function getUserProfile() {
            try {
                const response = await fetch(`${API_BASE}/profile`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const userData = await response.json();
                    currentUser = userData;
                    showUserDashboard();
                } else {
                    // Token might be invalid
                    localStorage.removeItem('authToken');
                    authToken = null;
                }
            } catch (error) {
                console.error('Failed to get user profile:', error);
            }
        }

        async function updateProfile() {
            const name = document.getElementById('updateName').value;
            const phone = document.getElementById('updatePhone').value;

            try {
                const response = await fetch(`${API_BASE}/profile`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ name, phone })
                });

                const data = await response.json();

                if (response.ok) {
                    currentUser = { ...currentUser, ...data.user };
                    updateUserInfo();
                    showSuccess('Profile updated successfully!');
                } else {
                    showError(data.error || 'Failed to update profile');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        async function upgradeSubscription() {
            try {
                const response = await fetch(`${API_BASE}/subscription/upgrade`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showSuccess('Subscription upgraded to Premium!');
                    // Refresh user profile to show updated subscription
                    getUserProfile();
                } else {
                    showError(data.error || 'Failed to upgrade subscription');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        async function addFriend() {
            const friendUid = document.getElementById('friendUid').value.trim();

            if (!friendUid) {
                showError('Please enter a friend UID');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/friends`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ friendUid })
                });

                const data = await response.json();

                if (response.ok) {
                    showSuccess('Friend added successfully!');
                    document.getElementById('friendUid').value = '';
                    // Refresh user profile to show updated friends count
                    getUserProfile();
                } else {
                    showError(data.error || 'Failed to add friend');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            }
        }

        function logout() {
            localStorage.removeItem('authToken');
            authToken = null;
            currentUser = null;
            showAuthSection();
            showSuccess('Logged out successfully!');
        }

        function showUserDashboard() {
            document.getElementById('authSection').classList.add('hidden');
            document.getElementById('userDashboard').classList.remove('hidden');
            updateUserInfo();
        }

        function showAuthSection() {
            document.getElementById('authSection').classList.remove('hidden');
            document.getElementById('userDashboard').classList.add('hidden');
            clearForm();
        }

        function updateUserInfo() {
            if (currentUser) {
                const subscription = currentUser.current_subscription || currentUser.subscription;
                const friendsCount = currentUser.friends_count || 0;

                document.getElementById('userInfo').innerHTML = `
                    <strong>User Information:</strong><br>
                    <strong>ID:</strong> ${currentUser.id || currentUser.uid || 'N/A'}<br>
                    <strong>Firebase UID:</strong> ${currentUser.firebaseUid || 'N/A'}<br>
                    <strong>Email:</strong> ${currentUser.email}<br>
                    <strong>Name:</strong> ${currentUser.name}<br>
                    <strong>Phone:</strong> ${currentUser.phone || 'Not provided'}<br>
                    <strong>Friends:</strong> ${friendsCount}<br>
                    <strong>Subscription:</strong> ${subscription ? subscription.plan : 'Free'}<br>
                    <strong>Created:</strong> ${currentUser.created_at ? new Date(currentUser.created_at).toLocaleDateString() : 'N/A'}
                `;

                document.getElementById('updateName').value = currentUser.name || '';
                document.getElementById('updatePhone').value = currentUser.phone || '';
            }
        }

        function clearForm() {
            document.getElementById('email').value = '';
            document.getElementById('password').value = '';
            document.getElementById('name').value = '';
            document.getElementById('phone').value = '';
            clearMessages();
        }

        function showError(message) {
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('successMessage').textContent = '';
        }

        function showSuccess(message) {
            document.getElementById('successMessage').textContent = message;
            document.getElementById('errorMessage').textContent = '';
        }

        function clearMessages() {
            document.getElementById('errorMessage').textContent = '';
            document.getElementById('successMessage').textContent = '';
        }
    </script>
</body>
</html>
