# Firebase Integration Setup Guide

This guide will help you connect your Firebase web app with your frontend and backend.

## 🔥 Firebase Configuration

Your Firebase project details:
- **Project ID**: `plover-c2b9b`
- **Auth Domain**: `plover-c2b9b.firebaseapp.com`
- **Storage Bucket**: `plover-c2b9b.firebasestorage.app`

## 📋 Prerequisites

1. **Firebase Project**: Your project `plover-c2b9b` is already configured
2. **Node.js**: Make sure you have Node.js installed
3. **MongoDB**: Local or cloud MongoDB instance

## 🚀 Setup Steps

### Step 1: Install Dependencies

Your dependencies are already installed, but if you need to add more:

```bash
npm install cors dotenv
```

### Step 2: Firebase Admin SDK Setup

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project `plover-c2b9b`
3. Go to **Project Settings** > **Service Accounts**
4. Click **Generate New Private Key**
5. Download the JSON file and save it as `firebase-service-account.json` in your project root

### Step 3: Environment Configuration

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Update `.env` with your values:
   ```env
   JWT_SECRET=your_super_secure_jwt_secret_key_change_this
   MONGODB_URI=mongodb://localhost:27017/plover-app
   PORT=5000
   GOOGLE_APPLICATION_CREDENTIALS=./firebase-service-account.json
   NODE_ENV=development
   ```

### Step 4: Enable Firebase Authentication

1. In Firebase Console, go to **Authentication** > **Sign-in method**
2. Enable the following providers:
   - **Email/Password**
   - **Google** (optional)
   - **Phone** (if using React Native)

### Step 5: Configure Firestore (Optional)

1. Go to **Firestore Database** in Firebase Console
2. Create database in **test mode** for development
3. Set up security rules as needed

## 📁 File Structure

Your project now has these key files:

```
your-project/
├── firebase-config-frontend.js     # Frontend Firebase config (✅ Updated)
├── firebase-config-backend.js      # Backend Firebase config
├── firebase-integration.js         # Complete integration functions (✅ New)
├── server-updated.js              # Updated server with proper integration (✅ New)
├── LoginPage.jsx                   # Updated login component (✅ Updated)
├── LoginScreen.js                  # React Native login (existing)
├── schemas.js                      # MongoDB schemas (existing)
├── .env.example                    # Environment variables template (✅ New)
└── package.json                    # Dependencies
```

## 🔧 Usage

### Frontend Integration

Use the functions from `firebase-integration.js`:

```javascript
import { signInWithEmail, signUpWithEmail, signInWithGoogle } from './firebase-integration';

// Sign up
const handleSignup = async () => {
  try {
    const result = await signUpWithEmail(email, password, name, phone);
    console.log('User signed up:', result.user);
  } catch (error) {
    console.error('Signup failed:', error.message);
  }
};

// Sign in
const handleLogin = async () => {
  try {
    const result = await signInWithEmail(email, password);
    console.log('User logged in:', result.user);
  } catch (error) {
    console.error('Login failed:', error.message);
  }
};
```

### Backend Integration

Use the updated server (`server-updated.js`):

```bash
# Start the server
node server-updated.js
```

The server provides these endpoints:
- `POST /api/signup` - User registration
- `POST /api/login` - User login
- `GET /api/profile` - Get user profile (protected)
- `PUT /api/profile` - Update user profile (protected)
- `GET /api/health` - Health check

## 🧪 Testing the Integration

### 1. Start MongoDB
```bash
# If using local MongoDB
mongod
```

### 2. Start Backend Server
```bash
node server-updated.js
```

### 3. Test Health Endpoint
```bash
curl http://localhost:5000/api/health
```

### 4. Test Frontend
Create a simple HTML file to test:

```html
<!DOCTYPE html>
<html>
<head>
    <title>Firebase Test</title>
</head>
<body>
    <script type="module">
        import { signUpWithEmail } from './firebase-integration.js';
        
        // Test signup
        signUpWithEmail('<EMAIL>', 'password123', 'Test User', '+**********')
            .then(result => console.log('Success:', result))
            .catch(error => console.error('Error:', error));
    </script>
</body>
</html>
```

## 🔐 Security Considerations

1. **Environment Variables**: Never commit `.env` file to version control
2. **JWT Secret**: Use a strong, random secret in production
3. **Firebase Rules**: Set up proper Firestore security rules
4. **CORS**: Configure CORS properly for production
5. **HTTPS**: Use HTTPS in production

## 🐛 Troubleshooting

### Common Issues:

1. **Firebase Admin not initialized**
   - Make sure service account key is downloaded and path is correct
   - Check GOOGLE_APPLICATION_CREDENTIALS environment variable

2. **CORS errors**
   - Make sure `cors` middleware is enabled in server
   - Check if frontend and backend URLs match

3. **Token verification failed**
   - Ensure Firebase project ID matches in both frontend and backend
   - Check if user exists in MongoDB after Firebase authentication

4. **MongoDB connection failed**
   - Make sure MongoDB is running
   - Check MONGODB_URI in .env file

## 📚 Next Steps

1. **Add more authentication methods** (Google, Facebook, etc.)
2. **Implement password reset functionality**
3. **Add user profile management**
4. **Set up proper error handling and logging**
5. **Deploy to production with proper security**

## 🆘 Need Help?

If you encounter issues:
1. Check the console logs for detailed error messages
2. Verify all environment variables are set correctly
3. Ensure Firebase project settings match your configuration
4. Test each component (Firebase, MongoDB, Express) separately
