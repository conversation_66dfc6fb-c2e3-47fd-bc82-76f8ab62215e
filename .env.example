# Environment Variables for Firebase Integration
# Copy this file to .env and fill in your actual values

# JWT Secret (change this to a secure random string in production)
JWT_SECRET=your_super_secure_jwt_secret_key_change_this

# MongoDB Connection
MONGODB_URI=mongodb://localhost:27017/plover-app

# Server Port
PORT=5000

# Firebase Admin SDK Configuration
# Option 1: Path to service account key file (recommended for development)
GOOGLE_APPLICATION_CREDENTIALS=./path-to-your-firebase-service-account.json

# Option 2: Service account key as JSON string (for deployment)
# FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"plover-c2b9b",...}

# Node Environment
NODE_ENV=development
