// Complete Firebase Integration for Frontend and Backend
// This file demonstrates how to connect your Firebase app with both frontend and backend

// ===== FRONTEND INTEGRATION =====
import { 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged 
} from 'firebase/auth';
import { auth } from './firebase-config-frontend';
import axios from 'axios';

// Configure Google Auth Provider
const googleProvider = new GoogleAuthProvider();

// API Base URL - Update this to match your backend server
const API_BASE_URL = 'http://localhost:5000/api';

// ===== AUTHENTICATION FUNCTIONS =====

/**
 * Sign up a new user with email and password
 */
export async function signUpWithEmail(email, password, name, phone) {
  try {
    // Create user in Firebase
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Get Firebase ID token
    const idToken = await user.getIdToken();
    
    // Send user data to your backend
    const response = await axios.post(`${API_BASE_URL}/signup`, {
      email,
      password,
      name,
      phone,
      idToken
    });
    
    console.log('User signed up successfully:', response.data);
    return {
      user: response.data.user,
      token: response.data.token
    };
  } catch (error) {
    console.error('Error signing up:', error);
    throw error;
  }
}

/**
 * Sign in user with email and password
 */
export async function signInWithEmail(email, password) {
  try {
    // Sign in with Firebase
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;
    
    // Get Firebase ID token
    const idToken = await user.getIdToken();
    
    // Verify with your backend and get JWT
    const response = await axios.post(`${API_BASE_URL}/login`, {
      idToken
    });
    
    // Store JWT token in localStorage
    localStorage.setItem('authToken', response.data.token);
    
    console.log('User signed in successfully:', response.data);
    return {
      user: response.data.user,
      token: response.data.token
    };
  } catch (error) {
    console.error('Error signing in:', error);
    throw error;
  }
}

/**
 * Sign in with Google
 */
export async function signInWithGoogle() {
  try {
    // Sign in with Google popup
    const userCredential = await signInWithPopup(auth, googleProvider);
    const user = userCredential.user;
    
    // Get Firebase ID token
    const idToken = await user.getIdToken();
    
    // Send to backend for verification
    const response = await axios.post(`${API_BASE_URL}/login`, {
      idToken
    });
    
    // Store JWT token
    localStorage.setItem('authToken', response.data.token);
    
    console.log('Google sign in successful:', response.data);
    return {
      user: response.data.user,
      token: response.data.token
    };
  } catch (error) {
    console.error('Error with Google sign in:', error);
    throw error;
  }
}

/**
 * Sign out user
 */
export async function signOutUser() {
  try {
    await signOut(auth);
    localStorage.removeItem('authToken');
    console.log('User signed out successfully');
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
}

/**
 * Listen to authentication state changes
 */
export function onAuthStateChange(callback) {
  return onAuthStateChanged(auth, callback);
}

// ===== API HELPER FUNCTIONS =====

/**
 * Get authorization headers for API calls
 */
export function getAuthHeaders() {
  const token = localStorage.getItem('authToken');
  return token ? { Authorization: `Bearer ${token}` } : {};
}

/**
 * Make authenticated API call
 */
export async function authenticatedRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${API_BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...getAuthHeaders()
      }
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return response.data;
  } catch (error) {
    if (error.response?.status === 401) {
      // Token expired or invalid, sign out user
      await signOutUser();
      throw new Error('Session expired. Please sign in again.');
    }
    throw error;
  }
}

// ===== USAGE EXAMPLES =====

/**
 * Example: Complete login flow
 */
export async function handleLogin(email, password) {
  try {
    const result = await signInWithEmail(email, password);
    
    // Redirect to dashboard or update UI state
    console.log('Login successful, user:', result.user);
    
    return result;
  } catch (error) {
    // Handle specific error cases
    if (error.code === 'auth/user-not-found') {
      throw new Error('No account found with this email');
    } else if (error.code === 'auth/wrong-password') {
      throw new Error('Incorrect password');
    } else if (error.code === 'auth/invalid-email') {
      throw new Error('Invalid email address');
    } else {
      throw new Error('Login failed. Please try again.');
    }
  }
}

/**
 * Example: Complete signup flow
 */
export async function handleSignup(email, password, name, phone) {
  try {
    const result = await signUpWithEmail(email, password, name, phone);
    
    // Redirect to dashboard or update UI state
    console.log('Signup successful, user:', result.user);
    
    return result;
  } catch (error) {
    // Handle specific error cases
    if (error.code === 'auth/email-already-in-use') {
      throw new Error('An account with this email already exists');
    } else if (error.code === 'auth/weak-password') {
      throw new Error('Password should be at least 6 characters');
    } else if (error.code === 'auth/invalid-email') {
      throw new Error('Invalid email address');
    } else {
      throw new Error('Signup failed. Please try again.');
    }
  }
}

/**
 * Example: Fetch user profile
 */
export async function getUserProfile() {
  return await authenticatedRequest('GET', '/profile');
}

/**
 * Example: Update user profile
 */
export async function updateUserProfile(profileData) {
  return await authenticatedRequest('PUT', '/profile', profileData);
}
