/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Provider } from '@firebase/component';
import { FirebaseApp } from '@firebase/app';
import { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';
import { MessagingInternalComponentName } from '@firebase/messaging-interop-types';
import { FirebaseAuthInternalName } from '@firebase/auth-interop-types';
/**
 * The metadata that should be supplied with function calls.
 * @internal
 */
export interface Context {
    authToken?: string;
    messagingToken?: string;
    appCheckToken: string | null;
}
/**
 * Helper class to get metadata that should be included with a function call.
 * @internal
 */
export declare class ContextProvider {
    readonly app: FirebaseApp;
    private auth;
    private messaging;
    private appCheck;
    private serverAppAppCheckToken;
    constructor(app: FirebaseApp, authProvider: Provider<FirebaseAuthInternalName>, messagingProvider: Provider<MessagingInternalComponentName>, appCheckProvider: Provider<AppCheckInternalComponentName>);
    getAuthToken(): Promise<string | undefined>;
    getMessagingToken(): Promise<string | undefined>;
    getAppCheckToken(limitedUseAppCheckTokens?: boolean): Promise<string | null>;
    getContext(limitedUseAppCheckTokens?: boolean): Promise<Context>;
}
