const express = require('express');
const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const cors = require('cors');
const bcrypt = require('bcrypt');
require('dotenv').config();

// Import your existing User schema
const { User } = require('./schemas');

const app = express();

// Middleware
app.use(express.json());
app.use(cors()); // Enable CORS for frontend-backend communication

// Environment variables
const JWT_SECRET = process.env.JWT_SECRET || 'plover_jwt_secret_key_2024_change_in_production';
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost/travel_app';
const PORT = process.env.PORT || 5000;

// Connect to MongoDB
mongoose.connect(MONGODB_URI)
  .then(() => console.log('✅ Connected to MongoDB'))
  .catch(err => {
    console.log('⚠️  MongoDB connection failed:', err.message);
    console.log('📝 Make sure MongoDB is running or update MONGODB_URI in .env');
  });

// Middleware to verify JWT token
const verifyJWT = (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'No token provided' });
  }

  const token = authHeader.split('Bearer ')[1];
  
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ error: 'Invalid token' });
  }
};

// ===== AUTHENTICATION ROUTES =====

// Signup API (Using your comprehensive schema)
app.post('/api/signup', async (req, res) => {
  const { email, password, name, phone, firebaseUid } = req.body;

  try {
    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [
        { email },
        { firebaseUid: firebaseUid || null }
      ]
    });

    if (existingUser) {
      return res.status(400).json({ error: 'User already exists with this email or Firebase UID' });
    }

    // Generate a demo Firebase UID if not provided
    const demoFirebaseUid = firebaseUid || `demo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create user with your comprehensive schema
    const user = new User({
      firebaseUid: demoFirebaseUid,
      email,
      password, // Will be hashed by schema pre-save hook
      phone,
      profile: {
        name,
        personal_details: {
          address: '',
          dob: null
        },
        subscription_history: [{
          plan: 'Free',
          start_date: new Date(),
          end_date: null
        }]
      },
      friends: []
    });

    await user.save();

    // Generate JWT
    const token = jwt.sign(
      {
        id: user._id,
        firebaseUid: user.firebaseUid,
        email: user.email
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    res.status(201).json({
      token,
      user: {
        id: user._id,
        firebaseUid: user.firebaseUid,
        email: user.email,
        name: user.profile.name,
        phone: user.phone,
        subscription: user.profile.subscription_history[0]
      }
    });

  } catch (error) {
    console.error('Signup error:', error);
    res.status(400).json({ error: error.message });
  }
});

// Login API (Using your comprehensive schema)
app.post('/api/login', async (req, res) => {
  const { email, password, firebaseUid } = req.body;

  try {
    // Find user in MongoDB by email or firebaseUid
    const query = firebaseUid ? { firebaseUid } : { email };
    const user = await User.findOne(query);

    if (!user) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }

    // Check password (if provided - for email/password login)
    if (password && user.password) {
      const isValidPassword = await bcrypt.compare(password, user.password);
      if (!isValidPassword) {
        return res.status(401).json({ error: 'Invalid email or password' });
      }
    }

    // Generate JWT
    const token = jwt.sign(
      {
        id: user._id,
        firebaseUid: user.firebaseUid,
        email: user.email
      },
      JWT_SECRET,
      { expiresIn: '7d' }
    );

    // Update last login
    user.updated_at = new Date();
    await user.save();

    res.json({
      token,
      user: {
        id: user._id,
        firebaseUid: user.firebaseUid,
        email: user.email,
        name: user.profile.name,
        phone: user.phone,
        subscription: user.profile.subscription_history[user.profile.subscription_history.length - 1],
        friends_count: user.friends.length
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ===== PROTECTED ROUTES =====

// Get user profile (Using your comprehensive schema)
app.get('/api/profile', verifyJWT, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      id: user._id,
      firebaseUid: user.firebaseUid,
      email: user.email,
      phone: user.phone,
      profile: {
        name: user.profile.name,
        personal_details: user.profile.personal_details,
        subscription_history: user.profile.subscription_history
      },
      friends: user.friends,
      friends_count: user.friends.length,
      current_subscription: user.profile.subscription_history[user.profile.subscription_history.length - 1],
      created_at: user.created_at,
      updated_at: user.updated_at
    });
  } catch (error) {
    console.error('Profile fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update user profile (Using your comprehensive schema)
app.put('/api/profile', verifyJWT, async (req, res) => {
  try {
    const { name, phone, address, dob } = req.body;

    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Update fields
    if (name) user.profile.name = name;
    if (phone !== undefined) user.phone = phone;
    if (address !== undefined) user.profile.personal_details.address = address;
    if (dob !== undefined) user.profile.personal_details.dob = dob ? new Date(dob) : null;

    await user.save();

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: user._id,
        firebaseUid: user.firebaseUid,
        email: user.email,
        name: user.profile.name,
        phone: user.phone,
        profile: user.profile
      }
    });
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ===== FRIENDS MANAGEMENT =====

// Get user's friends
app.get('/api/friends', verifyJWT, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('friends');
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      friends: user.friends,
      friends_count: user.friends.length
    });
  } catch (error) {
    console.error('Friends fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Add a friend
app.post('/api/friends', verifyJWT, async (req, res) => {
  try {
    const { friendUid } = req.body;

    if (!friendUid) {
      return res.status(400).json({ error: 'Friend UID is required' });
    }

    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if friend already exists
    const existingFriend = user.friends.find(friend => friend.uid === friendUid);
    if (existingFriend) {
      return res.status(400).json({ error: 'Friend already added' });
    }

    // Add friend
    user.friends.push({
      uid: friendUid,
      activities_count: 0
    });

    await user.save();

    res.json({
      message: 'Friend added successfully',
      friends_count: user.friends.length
    });
  } catch (error) {
    console.error('Add friend error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// ===== SUBSCRIPTION MANAGEMENT =====

// Get subscription history
app.get('/api/subscription', verifyJWT, async (req, res) => {
  try {
    const user = await User.findById(req.user.id).select('profile.subscription_history');
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      subscription_history: user.profile.subscription_history,
      current_subscription: user.profile.subscription_history[user.profile.subscription_history.length - 1]
    });
  } catch (error) {
    console.error('Subscription fetch error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update subscription (upgrade to Premium)
app.post('/api/subscription/upgrade', verifyJWT, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // End current subscription
    const currentSub = user.profile.subscription_history[user.profile.subscription_history.length - 1];
    if (currentSub && !currentSub.end_date) {
      currentSub.end_date = new Date();
    }

    // Add new Premium subscription
    user.profile.subscription_history.push({
      plan: 'Premium',
      start_date: new Date(),
      end_date: null
    });

    await user.save();

    res.json({
      message: 'Subscription upgraded to Premium',
      current_subscription: user.profile.subscription_history[user.profile.subscription_history.length - 1]
    });
  } catch (error) {
    console.error('Subscription upgrade error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    mongodb: mongoose.connection.readyState === 1 ? 'Connected' : 'Disconnected',
    server: 'Demo Server (without Firebase Admin)'
  });
});

// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({ 
    message: 'Backend server is running!',
    timestamp: new Date().toISOString()
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Backend Server with MongoDB Schema Integration`);
  console.log(`📱 API Base URL: http://localhost:${PORT}/api`);
  console.log(`🗄️  MongoDB: ${MONGODB_URI}`);
  console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`📊 Schema: Comprehensive User Schema with Firebase, Friends, Subscriptions`);
  console.log(`\n📋 Available endpoints:`);
  console.log(`   🔍 GET  /api/health - Health check`);
  console.log(`   🧪 GET  /api/test - Test endpoint`);
  console.log(`   👤 POST /api/signup - User registration`);
  console.log(`   🔐 POST /api/login - User login`);
  console.log(`   📄 GET  /api/profile - Get user profile (protected)`);
  console.log(`   ✏️  PUT  /api/profile - Update user profile (protected)`);
  console.log(`   👥 GET  /api/friends - Get user's friends (protected)`);
  console.log(`   ➕ POST /api/friends - Add a friend (protected)`);
  console.log(`   💳 GET  /api/subscription - Get subscription history (protected)`);
  console.log(`   ⬆️  POST /api/subscription/upgrade - Upgrade to Premium (protected)`);
});

module.exports = app;
